import React from 'react';
import { Route, Routes, Navigate, Outlet } from 'react-router-dom';
import RequireAuth from '@auth-kit/react-router/RequireAuth';
import { useAuth } from '@/features/auth/hooks/useAuth';

// Import admin pages
import AdminDashboard from '@/pages/AdminDashboard';
import AdminJobOversight from '@/pages/AdminJobOversight';
import Bookings from '@/pages/Bookings';
import Business from '@/pages/Business';
import { ProvidersTable } from '@/components/admin/ProvidersTable';
import { ProviderInvitations } from '@/components/admin/ProviderInvitations';
import { CustomerManagement } from '@/components/admin/CustomerManagement';
import { JobsManagement } from '@/components/admin/JobsManagement';
import { PaymentsManagement } from '@/components/admin/PaymentsManagement';
import { AdminMessagingInterface } from '@/components/admin/messaging/AdminMessagingInterface';
import { ReviewsManagement } from '@/components/admin/ReviewsManagement';
import { RewardsManagement } from '@/components/admin/RewardsManagement';
import { ReferralsManagement } from '@/components/admin/ReferralsManagement';
import { AdminSettings } from '@/components/admin/settings';

// Import the admin layout
import AdminLayout from '@/components/admin/AdminLayout';
import {Certificates} from "@/pages/Certificates.tsx";

/**
 * Admin Routes Component
 *
 * This component handles all routes that should only be accessible to admins.
 * It uses RequireAuth from @auth-kit/react-router to protect routes and redirects
 * non-admins to the appropriate page.
 */
const AdminRoutes: React.FC = () => {
  const { isAdmin, rolesLoading } = useAuth();

  // Admin check component
  const AdminCheck: React.FC = () => {
    // If roles are still loading, don't redirect yet
    if (rolesLoading) {
      // You could return a loading spinner here if desired
      return <div></div>;
    }

    // Once roles are loaded, check if user is admin
    return isAdmin ? <Outlet /> : <Navigate to="/" replace />;
  };

  return (
    <Routes>
      <Route element={
        <RequireAuth fallbackPath="/auth">
          <AdminCheck />
        </RequireAuth>
      }>
        {/* Apply the AdminLayout to all admin routes */}
        <Route element={<AdminLayout />}>
          {/* Admin routes */}
          <Route path="/" element={<AdminDashboard />} />
          <Route path="/dashboard" element={<AdminDashboard />} />
          <Route path="/job-oversight" element={<AdminJobOversight />} />
          <Route path="/bookings" element={<Bookings />} />
          <Route path="/business" element={<Business />} />
          <Route path="/providers" element={<ProvidersTable />} />
          <Route path="/provider-invitations" element={<ProviderInvitations />} />
          <Route path="/customers" element={<CustomerManagement />} />
          <Route path="/jobs" element={<JobsManagement />} />
          <Route path="/payments" element={<PaymentsManagement />} />
          <Route path="/messages" element={<AdminMessagingInterface />} />
          <Route path="/reviews" element={<ReviewsManagement />} />
          <Route path="/rewards" element={<RewardsManagement />} />
          <Route path="/referrals" element={<ReferralsManagement />} />
          <Route path="/settings" element={<AdminSettings />} />
          <Route path="/certificates" element={<Certificates />} />


          {/* Catch-all route for admin section */}
          <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default AdminRoutes;
